version = 1
[common]
# API mode, set to true to prevent loading local subscriptions or serving local files directly
api_mode = false

# Access token used for performing critical action through Web interface
api_access_token = "password"

# Default URLs, used when no URL is provided in request, use "|" to separate multiple subscription links, supports local files/URL
default_url = []

# Insert subscription links to requests. Can be used to add node(s) to all exported subscriptions.
enable_insert = false
# URLs to insert before subscription links, can be used to add node(s) to all exported subscriptions, supports local files/URL
insert_url = [""]
# Prepend inserted URLs to subscription links. Nodes in insert_url will be added to groups first with non-group-specific match pattern.
prepend_insert_url = false

# Exclude nodes which remarks match the following patterns. Supports regular expression.
exclude_remarks = ["(?i)(到期|流量|Expire|Traffic|时间|官网)"]

# Only include nodes which remarks match the following patterns. Supports regular expression.
#include_remarks = ["V3.*港"]

# Enable script support for filtering nodes
enable_filter = false

# Setting an external config file as default when none is specified, supports local files/URL
# default_external_config = "config/example_external_config.toml"

# The file scope limit of the 'rule_base' options in external configs.
base_path = "base"

# Clash config base used by the generator, supports local files/URL
clash_rule_base = "base/all_base.tpl"

# Surge config base used by the generator, supports local files/URL
surge_rule_base = "base/all_base.tpl"

# Surfboard config base used by the generator, supports local files/URL
surfboard_rule_base = "base/all_base.tpl"

# Mellow config base used by the generator, supports local files/URL
mellow_rule_base = "base/all_base.tpl"

# Quantumult X config base used by the generator, supports local files/URL
quan_rule_base = "base/all_base.tpl"

# Quantumult config base used by the generator, supports local files/URL
quanx_rule_base = "base/all_base.tpl"

# Loon config base used by the generator, supports local files/URL
loon_rule_base = "base/all_base.tpl"

# SSR config base used by the generator, supports local files/URL
sssub_rule_base = "base/all_base.tpl"

# sing-box config base used by the generator, supports local files/URL
singbox_rule_base = "base/all_base.tpl"

# 离线模式 - 禁用所有网络请求
proxy_config = "NONE"
proxy_ruleset = "NONE"
proxy_subscription = "NONE"

# Append a proxy type string ([SS] [SSR] [VMess]) to node remark.
append_proxy_type = false

# When requesting /sub, reload this config file first.
reload_conf_on_request = false

[node_pref]
sort_flag = false
filter_deprecated_nodes = false
append_sub_userinfo = true
clash_use_new_field_name = true
clash_proxies_style = "flow"
singbox_add_clash_modes = true

[managed_config]
# Append a '#!MANAGED-CONFIG' info to Surge configurations
write_managed_config = false

# Address prefix for MANAGED-CONFIG info, without the trailing "/".
managed_config_prefix = "http://127.0.0.1:25500"

# Managed config update interval in seconds, determine how long the config will be updated.
config_update_interval = 86400

# If config_update_strict is set to true, Surge will require a force update after the interval.
config_update_strict = false

# Device ID to be written to rewrite scripts for some version of Quantumult X 
quanx_device_id = ""

[surge_external_proxy]
resolve_hostname = false

[emojis]
add_emoji = false
remove_old_emoji = true

[rulesets]
# 离线模式 - 禁用规则集
enabled = false
overwrite_original_rules = false
update_ruleset_on_request = false

[clash_proxy_group]
# 离线模式 - 使用简单的代理组配置
custom_proxy_group = [
    "🚀 节点选择`select`.*",
    "🎯 全球直连`DIRECT",
    "🛑 广告拦截`REJECT",
    "🐟 漏网之鱼`select`[]🚀 节点选择`[]🎯 全球直连"
]

[server]
listen = "0.0.0.0"
port = 25500
serve_file_root = "web"

[advanced]
log_level = "info"
print_debug_info = false
max_pending_connections = 10240
max_concurrent_threads = 2
max_allowed_rulesets = 0
max_allowed_rules = 0
max_allowed_download_size = 0
enable_cache = true
cache_subscription = 60
cache_config = 300
cache_ruleset = 0
script_clean_context = true
async_fetch_ruleset = false
skip_failed_links = true

# 网络超时配置
network_timeout = 5
connect_timeout = 3
read_timeout = 5
