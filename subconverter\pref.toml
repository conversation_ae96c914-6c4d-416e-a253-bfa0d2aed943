version = 1
[common]
# API mode, set to true to prevent loading local subscriptions or serving local files directly
api_mode = false

# Access token used for performing critical action through Web interface
api_access_token = "password"

# Default URLs, used when no URL is provided in request, use "|" to separate multiple subscription links, supports local files/URL
default_url = []

# Insert subscription links to requests. Can be used to add node(s) to all exported subscriptions.
enable_insert = true
# URLs to insert before subscription links, can be used to add node(s) to all exported subscriptions, supports local files/URL
insert_url = [""]
# Prepend inserted URLs to subscription links. Nodes in insert_url will be added to groups first with non-group-specific match pattern.
prepend_insert_url = true

# Exclude nodes which remarks match the following patterns. Supports regular expression.
exclude_remarks = ["(?i)(到期|流量|Expire|Traffic|时间|官网|引导页?|网(\\s+)?址|官址|地址|导航|平台|网站|域名|付费|优惠|折扣|刷新|获取|订阅|群|取消|禁|产品|余额|更新|回国|telegram|t.me|频道|电报|售后|反馈|工单|私聊|维护|升级|邮箱|关闭|耗尽|关机|停机|故障|宕机|调整|修复|解决|重新|拥挤|测试|公测|过年|test|测速|https?://|重置|剩余|特殊|⭕|1️⃣|购买|暂时|临时|下载|调试|检查|干扰|热度|公告|官方|推迟|阻断|采购|好用|福利|精品|商用|Prepaid|疫情|感染|下架|投诉|屏蔽|邀请|欢迎|机场|返利|推广|佣金|广告|破解|不同|骗|店|YYDS|真香|关注|谢谢|大家|永久|浏览器|月付|打开|包月|套餐|以上|以下|通知|注册|活动|转换|保证|每天|分享|倒卖|搬运|苏小柠|王者荣耀|代练|去除|不合适|尽快|绑定|临时域名|禁止|登录|激活|账号|恢复|更换|搜索|失联|发布|失联|发布)"]

# Only include nodes which remarks match the following patterns. Supports regular expression.
#include_remarks = ["V3.*港"]

# Enable script support for filtering nodes
enable_filter = false
# Script used for filtering nodes. Supports inline script and script path. A "filter" function with 1 argument which is a node should be defined in the script.
# Example: Inline script: set value to content of script.
#          Script path: set value to "path:/path/to/script.js".
#filter_script = '''
#function filter(node) {
#    const info = JSON.parse(node.ProxyInfo);
#    if(info.EncryptMethod.includes('chacha20'))
#	    return true;
#    return false;
#}
#'''

# Setting an external config file as default when none is specified, supports local files/URL
# default_external_config = "config/example_external_config.toml"

# The file scope limit of the 'rule_base' options in external configs.
base_path = "base"

# Clash config base used by the generator, supports local files/URL
clash_rule_base = "base/all_base.tpl"

# Surge config base used by the generator, supports local files/URL
surge_rule_base = "base/all_base.tpl"

# Surfboard config base used by the generator, supports local files/URL
surfboard_rule_base = "base/all_base.tpl"

# Mellow config base used by the generator, supports local files/URL
mellow_rule_base = "base/all_base.tpl"

# Quantumult config base used by the generator, supports local files/URL
quan_rule_base = "base/all_base.tpl"

# Quantumult X config base used by the generator, supports local files/URL
quanx_rule_base = "base/all_base.tpl"

# Loon config base used by the generator, supports local files/URL
loon_rule_base = "base/all_base.tpl"

# Shadowsocks Android config base used by the generator, supports local files/URL
sssub_rule_base = "base/all_base.tpl"

# sing-box config base used by the generator, supports local files/URL
singbox_rule_base = "base/all_base.tpl"

# 代理配置 - 已禁用规则集下载功能
proxy_config = "NONE"
proxy_subscription = "NONE"

# Append a proxy type string ([SS] [SSR] [VMess]) to node remark.
append_proxy_type = false

# When requesting /sub, reload this config file first.
reload_conf_on_request = false

[[userinfo.stream_rule]]
# Rules to extract stream data from node
# Format: full_match_regex|new_format_regex
# where new_format_regex should be like "total=$1&left=$2&used=$3"
match = '^剩余流量：(.*?)\|总流量：(.*)$'
replace = 'total=$2&left=$1'

[[userinfo.stream_rule]]
match = '^剩余流量：(.*?) (.*)$'
replace = 'total=$1&left=$2'

[[userinfo.stream_rule]]
match = '^Bandwidth: (.*?)/(.*)$'
replace = 'used=$1&total=$2'

[[userinfo.stream_rule]]
match = '^.*剩余(.*?)(?:\s*?)@(?:.*)$'
replace = 'total=$1'

[[userinfo.time_rule]]
# Rules to extract expire time data from node
# Format: full_match_regex|new_format_regex
# where new_format_regex should follow this example: yyyy:mm:dd:hh:mm:ss
match = '^过期时间：(\d+)-(\d+)-(\d+) (\d+):(\d+):(\d+)$'
replace = '$1:$2:$3:$4:$5:$6'

[[userinfo.time_rule]]
match = '^到期时间:(\d+)-(\d+)-(\d+)$'
replace = '$1:$2:$3:0:0:0'

[[userinfo.time_rule]]
match = '^Smart Access expire: (\d+)/(\d+)/(\d+)$'
replace = '$1:$2:$3:0:0:0'

[node_pref]
#udp_flag = true
#tcp_fast_open_flag = false
#skip_cert_verify_flag = true
#tls13_flag = false

sort_flag = false
# Script used for sorting nodes. A "compare" function with 2 arguments which are the 2 nodes to be compared should be defined in the script. Supports inline script and script path.
# Examples can be seen at the filter_script option in [common] section.
#sort_script = '''
#function compare(node_a, node_b) {
#   return info_a.Remark > info_b.Remark;
#}
#'''

filter_deprecated_nodes = false
append_sub_userinfo = true
clash_use_new_field_name = true

# Generate style of the proxies section of Clash subscriptions.
# Supported styles: block, flow, compact
# Block: - name: name1    Flow: - {name: name1, key: value}    Compact: [{name: name1, key: value},{name: name2, key: value}]
#         key: value           - {name: name2, key: value}
#       - name: name2
#         key: value
clash_proxies_style = "flow"

# add Clash mode to sing-box rules, and add a GLOBAL group to end of outbounds
singbox_add_clash_modes = true

[[node_pref.rename_node]]
match = '\(?((x|X)?(\d+)(\.?\d+)?)((\s?倍率?)|(x|X))\)?'
replace = "$1x"

[managed_config]
# Append a '#!MANAGED-CONFIG' info to Surge configurations
write_managed_config = true

# Address prefix for MANAGED-CONFIG info, without the trailing "/".
managed_config_prefix = "http://127.0.0.1:25500"

# Managed config update interval in seconds, determine how long the config will be updated.
config_update_interval = 86400

# If config_update_strict is set to true, Surge will require a force update after the interval.
config_update_strict = false

# Device ID to be written to rewrite scripts for some version of Quantumult X 
quanx_device_id = ""

[surge_external_proxy]
#surge_ssr_path = "/usr/bin/ssr-local"
resolve_hostname = true

[emojis]
add_emoji = false
remove_old_emoji = true

[[emojis.emoji]]
#match = '(流量|时间|应急)'
#emoji = '🏳️‍🌈'
import = "snippets/emoji.toml"

# [[custom_groups]]
# name = "Auto"
# type = "url-test"
# rule = [".*"]
# url = "http://www.gstatic.com/generate_204"
# interval = 300
# tolerance = 150
# lazy = true

# [[custom_groups]]
# name = "Proxy"
# type = "select"
# rule = [".*", "[]DIRECT"]
# disable_udp = false

# [[custom_groups]]
# name = "LoadBalance"
# type = "load-balance"
# rule = [".*", "[]Proxy", "[]DIRECT"]
# interval = 100
# strategy = "consistent-hashing"
# url = "http://www.gstatic.com/generate_204"

[[custom_groups]]
import = "snippets/groups.toml"

# [ruleset] 部分已删除 - 完全禁用规则集功能以避免网络请求卡住

[template]
template_path = ""

[[template.globals]]
key = "clash.http_port"
value = "7890"

[[template.globals]]
key = "clash.socks_port"
value = "7891"

[[template.globals]]
key = "clash.allow_lan"
value = "true"

[[template.globals]]
key = "clash.log_level"
value = "info"

[[template.globals]]
key = "singbox.allow_lan"
value = "true"

[[template.globals]]
key = "singbox.mixed_port"
value = "2080"

[[aliases]]
uri = "/clash"
target = "/sub?target=clash"

[[aliases]]
uri = "/clashr"
target = "/sub?target=clashr"

[[aliases]]
uri = "/surge"
target = "/sub?target=surge"

[[aliases]]
uri = "/quan"
target = "/sub?target=quan"

[[aliases]]
uri = "/quanx"
target = "/sub?target=quanx"

[[aliases]]
uri = "/mellow"
target = "/sub?target=mellow"

[[aliases]]
uri = "/surfboard"
target = "/sub?target=surfboard"

[[aliases]]
uri = "/loon"
target = "/sub?target=loon"

[[aliases]]
uri = "/singbox"
target = "/sub?target=singbox"

[[aliases]]
uri = "/ss"
target = "/sub?target=ss"

[[aliases]]
uri = "/ssd"
target = "/sub?target=ssd"

[[aliases]]
uri = "/sssub"
target = "/sub?target=sssub"

[[aliases]]
uri = "/ssr"
target = "/sub?target=ssr"

[[aliases]]
uri = "/v2ray"
target = "/sub?target=v2ray"

[[aliases]]
uri = "/trojan"
target = "/sub?target=trojan"

[[aliases]]
uri = "/test"
target = "/render?path=templates/test.tpl"

#[[tasks]]
#name = "tick"
#cronexp = "0/10 * * * * ?"
#path = "tick.js"
#timeout = 3

[server]
listen = "0.0.0.0"
port = 25500
serve_file_root = "web"

[advanced]
log_level = "debug"
print_debug_info = true
max_pending_connections = 10240
max_concurrent_threads = 4
max_allowed_rulesets = 0
max_allowed_rules = 0
max_allowed_download_size = 0
enable_cache = true
cache_subscription = 60
cache_config = 300
script_clean_context = true
skip_failed_links = true

# 网络超时配置 - 添加以解决卡住问题
network_timeout = 10
connect_timeout = 5
read_timeout = 10
