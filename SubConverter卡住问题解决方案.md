# SubConverter 卡住问题解决方案

## 问题描述
SubConverter 在处理规则集时卡在 `Updating ruleset url 'rules/LocalAreaNetwork.list'` 这一步，无法继续执行。

## 问题原因分析

### 1. 网络请求超时
- SubConverter 尝试下载远程规则集文件
- 网络连接不稳定或目标服务器响应慢
- DNS解析失败

### 2. 代理配置问题
- 系统代理设置导致请求被阻止
- 代理服务器不可用

### 3. 防火墙阻止
- Windows防火墙或第三方安全软件阻止网络请求
- 企业网络环境限制

## 解决方案

### 🚀 方案1：使用修复脚本（推荐）

#### Windows:
```bash
fix-subconverter.bat
```

#### Linux/Mac:
```bash
chmod +x fix-subconverter.sh
./fix-subconverter.sh
```

### 🔧 方案2：手动配置修复

#### 1. 停止SubConverter进程
```bash
# Windows
taskkill /f /im subconverter-windows-amd.exe

# Linux/Mac
pkill -f subconverter
```

#### 2. 修改配置文件
编辑 `subconverter/pref.toml`，修改以下配置：

```toml
# 禁用代理
proxy_config = "NONE"
proxy_ruleset = "NONE"
proxy_subscription = "NONE"

# 禁用规则集更新
update_ruleset_on_request = false

# 添加超时设置
[advanced]
network_timeout = 10
connect_timeout = 5
read_timeout = 10
```

#### 3. 清理缓存
```bash
# 删除缓存目录
rm -rf subconverter/cache
```

#### 4. 重新启动
```bash
cd subconverter
./subconverter-windows-amd.exe  # Windows
./subconverter-linux-amd        # Linux
./subconverter-darwin-amd       # Mac
```

### 🛡️ 方案3：离线模式（最稳定）

使用完全离线的配置文件：

```bash
# 备份原配置
cp subconverter/pref.toml subconverter/pref.toml.backup

# 使用离线配置
cp subconverter/pref.offline.toml subconverter/pref.toml

# 重启SubConverter
```

### 🔍 方案4：网络诊断

#### 检查网络连接
```bash
# 测试基本网络
ping *******

# 测试DNS解析
nslookup raw.githubusercontent.com

# 测试HTTP请求
curl -I https://raw.githubusercontent.com/DivineEngine/Profiles/master/Surge/Ruleset/Unbreak.list
```

#### 检查代理设置
```bash
# Windows
netsh winhttp show proxy

# Linux/Mac
echo $http_proxy
echo $https_proxy
```

## 预防措施

### 1. 环境变量优化
在启动SubConverter前设置：

```bash
# Windows
set HTTP_TIMEOUT=10
set CONNECT_TIMEOUT=5

# Linux/Mac
export HTTP_TIMEOUT=10
export CONNECT_TIMEOUT=5
```

### 2. 防火墙配置
确保以下端口可以访问：
- 25500 (SubConverter默认端口)
- 80, 443 (HTTP/HTTPS)

### 3. 定期清理
```bash
# 定期清理缓存
rm -rf subconverter/cache

# 定期更新SubConverter
```

## 验证修复

### 1. 检查SubConverter状态
```bash
curl http://127.0.0.1:25500/version
```

### 2. 测试转换功能
```bash
curl "http://127.0.0.1:25500/sub?target=clash&url=your_subscription_url"
```

### 3. 查看日志
检查SubConverter控制台输出，确认没有网络错误。

## 常见错误和解决方法

### 错误1: "Connection timeout"
**解决**: 增加超时时间或使用离线模式

### 错误2: "DNS resolution failed"
**解决**: 更换DNS服务器或使用IP地址

### 错误3: "Proxy connection failed"
**解决**: 禁用代理设置或修复代理配置

### 错误4: "Permission denied"
**解决**: 以管理员权限运行或检查文件权限

## 性能优化建议

1. **减少并发线程**: `max_concurrent_threads = 2`
2. **启用缓存**: `enable_cache = true`
3. **跳过失败链接**: `skip_failed_links = true`
4. **禁用调试输出**: `print_debug_info = false`

## 联系支持

如果问题仍然存在，请提供：
1. 操作系统版本
2. SubConverter版本
3. 完整的错误日志
4. 网络环境描述
