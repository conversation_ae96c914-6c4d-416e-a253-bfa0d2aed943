import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Table, 
  Progress, 
  Tag, 
  Space, 
  Typography,
  Alert,
  Spin,
  List,
  Button,
  Badge,
  App
} from 'antd';
import {
  CloudServerOutlined,
  LinkOutlined,
  CheckCircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  GlobalOutlined,
  <PERSON>Outlined,
  <PERSON>CircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  Legend
} from 'recharts';
import { statsApi, tasksApi, configApi } from '../services/api';
import { SystemStats, SystemStatus } from '../types';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { message } = App.useApp();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState<any>({});
  const [tasks, setTasks] = useState<any[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [quickExecuteLoading, setQuickExecuteLoading] = useState<Record<string, boolean>>({});

  useEffect(() => {
    fetchData();
    // 每30秒刷新一次数据
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [statsResponse, chartResponse, tasksResponse, statusResponse] = await Promise.all([
        statsApi.getStats(),
        statsApi.getChartData('nodes', '24h'),
        tasksApi.getTasks(),
        configApi.getSystemStatus()
      ]);
      
      if (statsResponse.data.success) {
        setStats(statsResponse.data.data!);
      }
      
      if (chartResponse.data.success) {
        setChartData(chartResponse.data.data!);
      }

      if (tasksResponse.data.success) {
        setTasks(tasksResponse.data.data || []);
      }

      if (statusResponse.data.success) {
        setSystemStatus(statusResponse.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const mockLatencyData = [
    { time: '00:00', latency: 120 },
    { time: '04:00', latency: 95 },
    { time: '08:00', latency: 140 },
    { time: '12:00', latency: 110 },
    { time: '16:00', latency: 85 },
    { time: '20:00', latency: 130 },
  ];

  const mockCountryData = [
    { name: '美国', count: 1250, color: '#8884d8' },
    { name: '日本', count: 980, color: '#82ca9d' },
    { name: '香港', count: 756, color: '#ffc658' },
    { name: '新加坡', count: 432, color: '#ff7c7c' },
    { name: '其他', count: 582, color: '#8dd1e1' },
  ];

  const mockActivityData = [
    { date: '2024-01-15', crawled: 1200, active: 980 },
    { date: '2024-01-16', crawled: 1350, active: 1100 },
    { date: '2024-01-17', crawled: 1100, active: 890 },
    { date: '2024-01-18', crawled: 1400, active: 1200 },
    { date: '2024-01-19', crawled: 1250, active: 1050 },
    { date: '2024-01-20', crawled: 1500, active: 1300 },
    { date: '2024-01-21', crawled: 1320, active: 1150 },
  ];

  const quickExecute = async (type: 'collect' | 'process') => {
    setQuickExecuteLoading(prev => ({ ...prev, [type]: true }));
    
    try {
      const api = type === 'collect' ? tasksApi.quickCollect : tasksApi.quickProcess;
      const params = type === 'collect' 
        ? { all_config: true, overwrite: false, skip_check: true, num_threads: 64 }
        : { overwrite: false, num_threads: 64, timeout: 5000, retry: 3 };
      
      const response = await api(params);
      
      if (response.data.success) {
        message.success(`${type === 'collect' ? '代理收集' : '代理处理'}任务已启动`);
        fetchData(); // 刷新数据
      } else {
        message.error(response.data.error || '启动任务失败');
      }
    } catch (error) {
      message.error('网络错误');
    } finally {
      setQuickExecuteLoading(prev => ({ ...prev, [type]: false }));
    }
  };

  const recentTasks = [
    { id: 1, name: 'Telegram频道爬取', status: 'completed', nodes: 234, time: '2分钟前' },
    { id: 2, name: 'GitHub仓库扫描', status: 'running', nodes: 0, time: '正在进行' },
    { id: 3, name: '机场订阅更新', status: 'completed', nodes: 156, time: '15分钟前' },
    { id: 4, name: 'Twitter用户监控', status: 'failed', nodes: 0, time: '1小时前' },
  ];

  if (loading && !stats) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <Spin size="large" />
      </div>
    );
  }

  const successRate = stats?.success_rate || 85.6;
  const totalNodes = stats?.total_nodes || 4000;
  const activeNodes = stats?.active_nodes || 3400;
  const totalSubs = stats?.total_subscriptions || 45;
  const activeSubs = stats?.active_subscriptions || 38;

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>系统概览</Title>
        <Text type="secondary">代理池聚合器运行状态和统计信息</Text>
      </div>

      {/* 快速操作区 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} md={8}>
          <Card size="small" title="快速执行">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={quickExecuteLoading.collect}
                onClick={() => quickExecute('collect')}
                style={{ width: '100%' }}
              >
                代理收集 (collect.py)
              </Button>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                loading={quickExecuteLoading.process}
                onClick={() => quickExecute('process')}
                style={{ width: '100%' }}
              >
                代理处理 (process.py)
              </Button>
            </Space>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card size="small" title="任务状态">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                运行中任务: <Badge count={tasks.filter(t => t.status === 'running').length} style={{ backgroundColor: '#52c41a' }} />
              </div>
              <div>
                今日完成: <Badge count={tasks.filter(t => 
                  t.status === 'completed' && 
                  new Date(t.created_at).toDateString() === new Date().toDateString()
                ).length} style={{ backgroundColor: '#1890ff' }} />
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card size="small" title="系统状态">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>状态: <Tag color="green">运行正常</Tag></div>
              <div>
                运行时间: {systemStatus?.uptime || '未知'}
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 环境信息 */}
      {systemStatus && (
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card 
              size="small" 
              title="环境信息"
              extra={
                <Tag color="blue" icon={<GlobalOutlined />}>
                  Python {systemStatus.python_version}
                </Tag>
              }
            >
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Text type="secondary">Python版本:</Text>
                  <br />
                  <Text strong>{systemStatus.python_version}</Text>
                </Col>
                <Col span={12}>
                  <Text type="secondary">系统平台:</Text>
                  <br />
                  <Text>{systemStatus.platform.split('-')[0] || 'Unknown'}</Text>
                </Col>
                <Col span={12}>
                  <Text type="secondary">架构:</Text>
                  <br />
                  <Text>{systemStatus.architecture}</Text>
                </Col>
                <Col span={12}>
                  <Text type="secondary">处理器:</Text>
                  <br />
                  <Text ellipsis={{ tooltip: systemStatus.processor }}>
                    {systemStatus.processor || 'Unknown'}
                  </Text>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card size="small" title="系统资源">
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <Text type="secondary">内存使用:</Text>
                  <br />
                  <Text strong>{systemStatus.memory_used}</Text> / <Text>{systemStatus.memory_total}</Text>
                  <Progress 
                    percent={parseFloat(systemStatus.memory_percent)} 
                    size="small" 
                    style={{ margin: '4px 0' }}
                    strokeColor={parseFloat(systemStatus.memory_percent) > 80 ? '#ff4d4f' : '#52c41a'}
                  />
                </Col>
                <Col span={12}>
                  <Text type="secondary">CPU使用率:</Text>
                  <br />
                  <Text strong>{systemStatus.cpu_usage}</Text>
                  <br />
                  <Text type="secondary">核心数: {systemStatus.cpu_count}</Text>
                </Col>
                <Col span={24}>
                  <Text type="secondary">磁盘使用:</Text>
                  <br />
                  <Text strong>{systemStatus.disk_usage.used}</Text> / <Text>{systemStatus.disk_usage.total}</Text>
                  <Progress 
                    percent={parseFloat(systemStatus.disk_usage.percent)} 
                    size="small" 
                    style={{ margin: '4px 0' }}
                    strokeColor={parseFloat(systemStatus.disk_usage.percent) > 85 ? '#ff4d4f' : '#52c41a'}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      </Row>

      {/* 状态提醒 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Alert
            message={tasks.some(t => t.status === 'running') ? "有任务正在运行" : "系统运行正常"}
            description={tasks.some(t => t.status === 'running') 
              ? `当前有 ${tasks.filter(t => t.status === 'running').length} 个任务正在执行中`
              : "所有核心服务正常运行，点击上方按钮可快速执行原项目功能"
            }
            type={tasks.some(t => t.status === 'running') ? "info" : "success"}
            showIcon
            closable
          />
        </Col>
      </Row>

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总节点数"
              value={totalNodes}
              prefix={<CloudServerOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={
                <span style={{ fontSize: 14 }}>
                  <ArrowUpOutlined style={{ color: '#3f8600' }} /> 12%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="可用节点"
              value={activeNodes}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <Progress 
              percent={Math.round((activeNodes / totalNodes) * 100)} 
              size="small" 
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="订阅源"
              value={activeSubs}
              prefix={<LinkOutlined />}
              suffix={`/ ${totalSubs}`}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="成功率"
              value={successRate}
              precision={1}
              suffix="%"
              prefix={<RocketOutlined />}
              valueStyle={{ color: successRate > 80 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 节点活动趋势 */}
        <Col xs={24} lg={16}>
          <Card title="节点活动趋势" extra={<Text type="secondary">最近7天</Text>}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockActivityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="crawled" 
                  stroke="#1890ff" 
                  name="爬取节点"
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="active" 
                  stroke="#52c41a" 
                  name="可用节点"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 节点分布 */}
        <Col xs={24} lg={8}>
          <Card title="节点地理分布">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mockCountryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {mockCountryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 延迟监控 */}
        <Col xs={24} lg={12}>
          <Card title="平均延迟趋势" extra={<Text type="secondary">最近24小时</Text>}>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={mockLatencyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}ms`, '延迟']} />
                <Line 
                  type="monotone" 
                  dataKey="latency" 
                  stroke="#ff7875" 
                  strokeWidth={2}
                  dot={{ fill: '#ff7875', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 最近任务 */}
        <Col xs={24} lg={12}>
          <Card 
            title="最近任务" 
            extra={<Text type="secondary">实时更新</Text>}
          >
            <List
              size="small"
              dataSource={recentTasks}
              renderItem={(task) => (
                <List.Item>
                  <div style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Space>
                        <Text strong>{task.name}</Text>
                        <Tag color={
                          task.status === 'completed' ? 'success' : 
                          task.status === 'running' ? 'processing' : 'error'
                        }>
                          {task.status === 'completed' ? '已完成' : 
                           task.status === 'running' ? '运行中' : '失败'}
                        </Tag>
                      </Space>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {task.time}
                      </Text>
                    </div>
                    {task.nodes > 0 && (
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        获得 {task.nodes} 个节点
                      </Text>
                    )}
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;