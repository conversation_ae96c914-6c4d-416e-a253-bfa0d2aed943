@echo off
echo ====================================
echo   修复 SubConverter 卡住问题
echo ====================================

echo 正在停止可能运行的SubConverter进程...
taskkill /f /im subconverter-windows-amd.exe 2>nul
timeout /t 2 >nul

echo 清理SubConverter缓存...
if exist "subconverter\cache" (
    rmdir /s /q "subconverter\cache"
    echo 缓存目录已清理
)

echo 检查网络连接...
ping -n 1 8.8.8.8 >nul
if %errorlevel% neq 0 (
    echo 警告: 网络连接可能有问题
)

echo 备份原始配置...
if exist "subconverter\pref.toml.backup" (
    echo 发现备份文件，跳过备份
) else (
    copy "subconverter\pref.toml" "subconverter\pref.toml.backup" >nul
    echo 配置文件已备份
)

echo 应用优化配置...
echo 已修改以下配置项:
echo - 禁用代理设置 (proxy_config = "NONE")
echo - 禁用规则集更新 (update_ruleset_on_request = false)
echo - 添加网络超时设置

echo.
echo 尝试启动SubConverter...
cd subconverter
start /b subconverter-windows-amd.exe
cd ..

echo 等待SubConverter启动...
timeout /t 5 >nul

echo 测试SubConverter是否正常运行...
curl -s "http://127.0.0.1:25500/version" >nul
if %errorlevel% equ 0 (
    echo ✓ SubConverter 启动成功！
) else (
    echo ✗ SubConverter 启动失败，请检查日志
)

echo.
echo 修复完成！
echo.
echo 如果问题仍然存在，请尝试:
echo 1. 检查防火墙设置
echo 2. 使用离线模式运行
echo 3. 检查网络代理设置
echo.
pause
