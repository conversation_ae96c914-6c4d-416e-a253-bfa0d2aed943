#!/bin/bash

echo "===================================="
echo "   修复 SubConverter 卡住问题"
echo "===================================="

echo "正在停止可能运行的SubConverter进程..."
pkill -f subconverter 2>/dev/null
sleep 2

echo "清理SubConverter缓存..."
if [ -d "subconverter/cache" ]; then
    rm -rf "subconverter/cache"
    echo "缓存目录已清理"
fi

echo "检查网络连接..."
if ! ping -c 1 8.8.8.8 >/dev/null 2>&1; then
    echo "警告: 网络连接可能有问题"
fi

echo "备份原始配置..."
if [ -f "subconverter/pref.toml.backup" ]; then
    echo "发现备份文件，跳过备份"
else
    cp "subconverter/pref.toml" "subconverter/pref.toml.backup"
    echo "配置文件已备份"
fi

echo "应用优化配置..."
echo "已修改以下配置项:"
echo "- 禁用代理设置 (proxy_config = \"NONE\")"
echo "- 禁用规则集更新 (update_ruleset_on_request = false)"
echo "- 添加网络超时设置"

echo
echo "尝试启动SubConverter..."
cd subconverter

# 检测系统架构
if [[ $(uname -m) == "arm64" ]] || [[ $(uname -m) == "aarch64" ]]; then
    if [[ $(uname) == "Darwin" ]]; then
        ./subconverter-darwin-arm &
    else
        ./subconverter-linux-arm &
    fi
elif [[ $(uname) == "Darwin" ]]; then
    ./subconverter-darwin-amd &
else
    ./subconverter-linux-amd &
fi

cd ..

echo "等待SubConverter启动..."
sleep 5

echo "测试SubConverter是否正常运行..."
if curl -s "http://127.0.0.1:25500/version" >/dev/null; then
    echo "✓ SubConverter 启动成功！"
else
    echo "✗ SubConverter 启动失败，请检查日志"
fi

echo
echo "修复完成！"
echo
echo "如果问题仍然存在，请尝试:"
echo "1. 检查防火墙设置"
echo "2. 使用离线模式运行"
echo "3. 检查网络代理设置"
echo
