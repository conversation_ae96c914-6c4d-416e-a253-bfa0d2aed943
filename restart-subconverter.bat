@echo off
echo ====================================
echo   重启 SubConverter (已删除规则集)
echo ====================================

echo 正在停止 SubConverter 进程...
taskkill /f /im subconverter-windows-amd.exe 2>nul
timeout /t 2 >nul

echo 清理缓存目录...
if exist "subconverter\cache" (
    rmdir /s /q "subconverter\cache"
    echo 缓存已清理
)

echo 删除的规则集相关配置:
echo - [ruleset] 整个配置段
echo - proxy_ruleset 代理设置
echo - 所有远程规则集下载配置
echo - snippets/rulesets.toml 中的所有规则

echo.
echo 正在启动 SubConverter...
cd subconverter
start /b subconverter-windows-amd.exe
cd ..

echo 等待启动...
timeout /t 3 >nul

echo 测试 SubConverter 状态...
curl -s "http://127.0.0.1:25500/version" >nul
if %errorlevel% equ 0 (
    echo ✓ SubConverter 启动成功！
    echo ✓ 不会再卡在规则集下载步骤
) else (
    echo ✗ 启动失败，请检查控制台输出
)

echo.
echo 现在 SubConverter 将:
echo - 跳过所有规则集下载
echo - 使用基础代理组配置
echo - 快速启动，不会卡住
echo.
pause
